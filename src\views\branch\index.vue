<template>
  <div class="branch-container">
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧分支列表区域 -->
      <div class="branch-list-section">
        <div class="section-header">
          <h3>分支列表</h3>
          <el-button type="text"  size="small" @click="handleCreateBranch" v-if="isAdmin">
            <i class="el-icon-plus"></i>
            新建分支
          </el-button>
        </div>

        <div
          class="branch-list"
          v-loading="branchListLoading"
          element-loading-text="正在加载..."
        >
          <!-- 分支卡片 -->
          <div
            v-for="branch in branchList"
            :key="branch.id"
            class="branch-card"
            :class="{ 'active': selectedBranch && selectedBranch.id === branch.id }"
            @click="selectBranch(branch)"
          >
            <div class="branch-card-header">
              <div class="branch-name">
                <i class="el-icon-s-operation"></i>
                {{ branch.name }}
              </div>
              <div class="branch-actions" v-if="isAdmin">
                <i class="el-icon-delete delete-icon" @click.stop="handleDelete(branch)" title="删除分支"></i>
              </div>
            </div>

            <div class="branch-card-content">
              <div class="branch-info">
                <div class="info-item">
                  <span class="label">检测类型:</span>
                  <span class="value">{{ getBranchType(branch) }}</span>
                </div>
                <div class="info-item">
                  <span class="label">告警数:</span>
                  <span class="value">{{ branch.error_rule_num || 0 }}</span>
                </div>
                <div class="info-item">
                  <span class="label">最近扫描时间:</span>
                  <span class="value">{{ formatDate(branch.scan_time) || '暂未扫描' }}</span>
                </div>
              </div>
            </div>


          </div>

          <!-- 空状态 -->
          <div v-if="branchList.length === 0 && !branchListLoading" class="empty-state">
            <i class="el-icon-folder-opened"></i>
            <p>暂无分支数据</p>
          </div>
        </div>
      </div>

      <!-- 右侧分支详情区域 -->
      <div class="branch-detail-section">
        <div class="section-header">
          <h3>{{ selectedBranch ? '编辑分支' : '新建分支' }}</h3>
        </div>

        <div class="detail-content">
          <!-- 分支表单 (新建/编辑共用) -->
          <div v-if="showCreateForm || selectedBranch" class="create-branch-form">
            <el-form
              ref="branchForm"
              :model="currentForm"
              :rules="createRules"
              label-width="120px"
              label-position="right"
            >
              <el-form-item label="分支名称" prop="branchName">
                <el-input
                  v-model="currentForm.branchName"
                  placeholder="请输入分支名称"
                  maxlength="30"
                  clearable
                  :disabled="!!selectedBranch"
                />
              </el-form-item>

              <!-- 资源检查配置 -->
              <el-divider content-position="left">资源检查配置</el-divider>

              <!-- 资源配置区域 - 左右布局 -->
              <div class="resource-layout">
                <!-- 左侧：美术资源配置 -->
                <div class="resource-section art-section">
                  <div class="resource-header">
                    <span class="resource-title">美术资源检查</span>
                    <el-switch
                      v-model="currentForm.enableArtCheck"
                      active-text="开启"
                      inactive-text="关闭"
                    />
                  </div>

                  <!-- 美术资源配置 -->
                  <div v-if="currentForm.enableArtCheck" class="resource-config">
                    <!-- 资源忽略路径 -->
                    <el-form-item label="资源忽略路径" label-width="200">
                      <el-tag
                        v-for="path in currentForm.artConfig.ignorePath"
                        :key="path"
                        closable
                        :disable-transitions="false"
                        type=""
                        @close="removeArtIgnorePath(path)"
                      >
                        {{ path }}
                      </el-tag>
                      <el-input
                        v-if="artInputVisible"
                        ref="artSaveTagInput"
                        v-model="artIgnorePath"
                        class="input-new-tag"
                        size="small"
                        maxlength="20"
                        @keyup.enter.native="handleArtInputConfirm"
                        @blur="handleArtInputConfirm"
                      />
                      <el-button
                        v-else
                        class="button-new-tag"
                        size="mini"
                        icon="el-icon-plus"
                        @click="showArtInput"
                      >添加</el-button>
                    </el-form-item>

                    <el-form-item label="上传rule_config文件" label-width="200" prop="artConfig.ruleConfigFile">
                      <!-- 编辑模式 -->
                      <div v-if="selectedBranch">
                        <!-- 已有文件：显示文件信息，不允许重新上传 -->
                        <div v-if="selectedBranch.art_filename" class="uploaded-file-display">
                          <div class="file-item">
                            <i class="el-icon-document"></i>
                            <span class="file-name" @click="downloadRuleConfigFile" title="点击下载">
                              {{ selectedBranch.art_filename }}
                            </span>
                            <el-tag size="mini" type="success">已上传</el-tag>
                          </div>
                          <!-- <div class="file-tip">
                            <i class="el-icon-info"></i>
                            <span>文件已上传，如需更换请联系管理员</span>
                          </div> -->
                        </div>
                        <!-- 无文件：允许上传 -->
                        <div v-else>
                          <el-upload
                            ref="ruleConfigUpload"
                            action=""
                            accept=".json"
                            :on-change="handleRuleConfigChange"
                            :on-remove="handleRuleConfigRemove"
                            :auto-upload="false"
                            :file-list="ruleConfigFileList"
                            :limit="1"
                          >
                            <el-button size="small" type="primary">
                              <i class="el-icon-upload"></i>
                              选择 rule_config.json
                            </el-button>
                            <div slot="tip" class="el-upload__tip">
                              只能上传json文件，且不超过10MB
                            </div>
                          </el-upload>
                        </div>
                      </div>
                      <!-- 新建模式：文件上传 -->
                      <el-upload
                        v-else
                        ref="ruleConfigUpload"
                        action=""
                        accept=".json"
                        :on-change="handleRuleConfigChange"
                        :on-remove="handleRuleConfigRemove"
                        :auto-upload="false"
                        :file-list="ruleConfigFileList"
                        :limit="1"
                      >
                        <el-button size="small" type="primary">
                          <i class="el-icon-upload"></i>
                          选择 rule_config.json
                        </el-button>
                        <div slot="tip" class="el-upload__tip">
                          只能上传json文件，且不超过10MB
                        </div>
                      </el-upload>
                    </el-form-item>
                  </div>
                </div>

                <!-- 右侧：表格资源配置 -->
                <div class="resource-section table-section">
                  <div class="resource-header">
                    <span class="resource-title">表格资源检查</span>
                    <el-switch
                      v-model="currentForm.enableTableCheck"
                      active-text="开启"
                      inactive-text="关闭"
                    />
                  </div>

                  <!-- 表格资源配置 -->
                  <div v-if="currentForm.enableTableCheck" class="resource-config">
                    <!-- 表格文件后缀 -->
                    <el-form-item label="表格文件后缀" prop="tableConfig.tableSuffix">
                      <el-input
                        v-model="currentForm.tableConfig.tableSuffix"
                        maxlength="30"
                        placeholder="请输入后缀如: .xlsx"
                      />
                    </el-form-item>

                    <!-- 表头数据所在行 -->
                    <el-form-item label="表头数据所在行" prop="tableConfig.head_row">
                      <el-input
                        v-model.number="currentForm.tableConfig.head_row"
                        maxlength="30"
                        placeholder="请输入表格的表头数据所在的行数"
                      />
                    </el-form-item>

                    <!-- 表头数据所在行(中文备注) -->
                    <el-form-item label="表头数据所在行(中文备注)" prop="tableConfig.head_row_cn">
                      <el-input
                        v-model="currentForm.tableConfig.head_row_cn"
                        maxlength="30"
                        placeholder="请输入表格的表头数据(中文备注)所在的行数"
                      />
                    </el-form-item>

                    <!-- 配置数据开始行 -->
                    <el-form-item label="配置数据开始行" prop="tableConfig.row_start">
                      <el-input
                        v-model.number="currentForm.tableConfig.row_start"
                        maxlength="30"
                        placeholder="请输入表格的配置数据开始行数"
                      />
                    </el-form-item>

                    <!-- 配置数据开始列 -->
                    <el-form-item label="配置数据开始列" prop="tableConfig.col_start">
                      <el-input
                        v-model.number="currentForm.tableConfig.col_start"
                        maxlength="30"
                        placeholder="请输入表格的配置数据开始列数"
                      />
                    </el-form-item>

                    <el-form-item label="仓库类型" prop="tableConfig.type">
                      <el-select
                        v-model="currentForm.tableConfig.type"
                        placeholder="请选择仓库类型"
                        style="width: 100%"
                        :disabled="hasRepositoryConfigured"
                      >
                        <el-option label="Git" value="git" />
                        <el-option label="SVN" value="svn" />
                      </el-select>
                    </el-form-item>

                    <el-form-item
                      v-if="currentForm.tableConfig.type"
                      label="仓库链接"
                      prop="tableConfig.url"
                    >
                      <el-input
                        v-model="currentForm.tableConfig.url"
                        placeholder="请输入仓库链接"
                        :disabled="hasRepositoryConfigured"
                      />
                    </el-form-item>

                    <el-form-item
                      v-if="currentForm.tableConfig.type === 'git'"
                      label="分支名称"
                      prop="tableConfig.gitBranch"
                    >
                      <el-input
                        v-model="currentForm.tableConfig.gitBranch"
                        placeholder="请输入Git分支名称"
                        :disabled="hasRepositoryConfigured"
                      />
                    </el-form-item>

                    <el-form-item
                      v-if="currentForm.tableConfig.type"
                      label="用户名"
                      prop="tableConfig.username"
                    >
                      <el-input
                        v-model="currentForm.tableConfig.username"
                        placeholder="请输入用户名"
                        :disabled="hasRepositoryConfigured"
                      />
                    </el-form-item>

                    <el-form-item
                      v-if="currentForm.tableConfig.type"
                      label="密码"
                      prop="tableConfig.password"
                    >
                      <el-input
                        v-model="currentForm.tableConfig.password"
                        type="password"
                        show-password
                        placeholder="请输入密码"
                        :disabled="hasRepositoryConfigured"
                      />
                    </el-form-item>
                  </div>
                </div>
              </div>

              <!-- 操作按钮 -->
              <el-form-item v-if="isAdmin">
                <el-button type="primary" @click="submitForm" :loading="createLoading || editLoading">
                  {{ selectedBranch ? '保存修改' : '创建分支' }}
                </el-button>
                <el-button @click="cancelForm">取消</el-button>
              </el-form-item>
            </el-form>
          </div>




        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {getProjectBranchs,deleteBranch,createBranch,editBranch,downloadRuleConfigFile} from '@/api/project'
import { mapState } from 'vuex'
export default {
  name: 'BranchIndex',
  data() {
    return {
      projectId: '',
      branchList: [],
      branchListLoading: false,
      selectedBranch: null,
      showCreateForm: false,
      createLoading: false,
      editLoading: false,

      // 新建分支表单数据
      createForm: {
        branchName: '',
        enableTableCheck: false,
        enableArtCheck: false,
        tableConfig: {
          tableSuffix: '',
          head_row: '',
          head_row_cn: '',
          row_start: '',
          col_start: '',
          type: '',
          url: '',
          gitBranch: '',
          username: '',
          password: ''
        },
        artConfig: {
          ignorePath: [],
          ruleConfigFile: null
        }
      },

      // 文件列表
      ruleConfigFileList: [],
      editRuleConfigFileList: [],

      // 资源忽略路径相关
      artInputVisible: false,
      artIgnorePath: '',
      editArtInputVisible: false,
      editArtIgnorePath: '',

      // 编辑表单数据
      editForm: {
        branchName: '',
        enableTableCheck: false,
        enableArtCheck: false,
        tableConfig: {
          tableSuffix: '',
          head_row: '',
          head_row_cn: '',
          row_start: '',
          col_start: '',
          type: '',
          url: '',
          gitBranch: '',
          username: '',
          password: ''
        },
        artConfig: {
          ignorePath: [],
          ruleConfigFile: null
        }
      },

      // 表单验证规则
      createRules: {
        branchName: [
          { required: true, message: '请输入分支名称', trigger: 'blur' },
          { min: 1, max: 30, message: '分支名称长度在 1 到 30 个字符', trigger: 'blur' }
        ],
        'tableConfig.tableSuffix': [
          {
            required: true,
            message: '文件后缀必填',
            trigger: 'blur',
            validator: (rule, value, callback) => { // eslint-disable-line no-unused-vars
              if (this.currentForm.enableTableCheck && !value) {
                callback(new Error('文件后缀必填'));
              } else {
                callback();
              }
            }
          }
        ],
        'tableConfig.head_row': [
          {
            required: true,
            message: '表头数据所在行必填',
            trigger: 'blur',
            validator: (rule, value, callback) => { // eslint-disable-line no-unused-vars
              if (this.currentForm.enableTableCheck) {
                if (value === null || value === undefined || value === '') {
                  callback(new Error('表头数据所在行必填'));
                } else if (!Number.isInteger(value)) {
                  callback(new Error('请输入整数值'));
                } else {
                  callback();
                }
              } else {
                callback();
              }
            }
          }
        ],
        'tableConfig.row_start': [
          {
            required: true,
            message: '开始行数必填',
            trigger: 'blur',
            validator: (rule, value, callback) => { // eslint-disable-line no-unused-vars
              if (this.currentForm.enableTableCheck) {
                if (value === null || value === undefined || value === '') {
                  callback(new Error('开始行数必填'));
                } else if (!Number.isInteger(value)) {
                  callback(new Error('请输入整数值'));
                } else {
                  callback();
                }
              } else {
                callback();
              }
            }
          }
        ],
        'tableConfig.col_start': [
          {
            required: true,
            message: '开始列数必填',
            trigger: 'blur',
            validator: (rule, value, callback) => { // eslint-disable-line no-unused-vars
              if (this.currentForm.enableTableCheck) {
                if (value === null || value === undefined || value === '') {
                  callback(new Error('开始列数必填'));
                } else if (!Number.isInteger(value)) {
                  callback(new Error('请输入整数值'));
                } else {
                  callback();
                }
              } else {
                callback();
              }
            }
          }
        ],
        'tableConfig.type': [
          { required: true, message: '请选择仓库类型', trigger: 'change' }
        ],
        'tableConfig.url': [
          { required: true, message: '请输入仓库链接', trigger: 'blur' }
        ],
        'tableConfig.gitBranch': [
          { required: true, message: '请输入Git分支名称', trigger: 'blur' }
        ],
        'tableConfig.username': [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        'tableConfig.password': [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ],
        'artConfig.ruleConfigFile': [
          {
            required: true,
            message: '请上传rule_config文件',
            trigger: 'change',
            validator: (rule, value, callback) => { // eslint-disable-line no-unused-vars
              if (this.currentForm.enableArtCheck) {
                // 编辑模式下，如果已有文件则不需要验证
                if (this.selectedBranch && this.selectedBranch.art_filename) {
                  callback();
                  return;
                }
                // 新建模式或编辑模式下没有已有文件，则需要上传新文件
                if (!value) {
                  callback(new Error('请上传rule_config文件'));
                } else {
                  callback();
                }
              } else {
                callback();
              }
            }
          }
        ]
      }
    };
  },
  computed: {
     ...mapState('user', ['isAdmin']),
    // 当前使用的表单数据（新建时用createForm，编辑时用editForm）
    currentForm() {
      return this.selectedBranch ? this.editForm : this.createForm;
    },

    // 判断是否已经配置过仓库信息
    hasRepositoryConfigured() {
      if (!this.selectedBranch) {
        return false; // 新建模式，没有配置过
      }

      // 检查是否有repository_info数据
      const repositoryInfo = this.selectedBranch.repository_info && this.selectedBranch.repository_info.length > 0
        ? this.selectedBranch.repository_info[0]
        : {};

      // 如果有仓库类型和仓库链接，则认为已经配置过
      return !!(repositoryInfo.stype && repositoryInfo.url);
    }
  },
  created() {
    // 从路由参数中获取projectId
    this.projectId = this.$route.params.projectId;
    this.fetchBranchList();
    // 默认显示新建分支表单
    this.showCreateForm = true;
  },
  watch: {
    '$route'(to, from) {
      // 监听路由变化，更新projectId
      if (to.params.projectId !== from.params.projectId) {
        this.projectId = to.params.projectId;
        this.fetchBranchList();
      }
    },

    // 监听表格资源开关变化
    'createForm.enableTableCheck'(newVal) {
      if (!newVal) {
        // 关闭时清空表格配置
        this.createForm.tableConfig = {
          tableSuffix: '',
          head_row: '',
          head_row_cn: '',
          row_start: '',
          col_start: '',
          type: '',
          url: '',
          gitBranch: '',
          username: '',
          password: ''
        };
      }
    },

    // 监听美术资源开关变化
    'createForm.enableArtCheck'(newVal) {
      if (!newVal) {
        // 关闭时清空美术配置
        this.createForm.artConfig = {
          ignorePath: [],
          ruleConfigFile: null
        };
        this.ruleConfigFileList = [];
        this.artInputVisible = false;
        this.artIgnorePath = '';
      }
    },

    // 监听仓库类型变化
    'createForm.tableConfig.type'(newVal) {
      if (newVal !== 'git') {
        // 非Git时清空Git分支名称
        this.createForm.tableConfig.gitBranch = '';
      }
    },

    // 监听编辑表单的表格资源开关变化
    'editForm.enableTableCheck'(newVal) {
      if (!newVal) {
        // 关闭时清空表格配置
        this.editForm.tableConfig = {
          tableSuffix: '',
          head_row: '',
          head_row_cn: '',
          row_start: '',
          col_start: '',
          type: '',
          url: '',
          gitBranch: '',
          username: '',
          password: ''
        };
      }
    },

    // 监听编辑表单的美术资源开关变化
    // 'editForm.enableArtCheck'(newVal) {
    //   if (!newVal) {
    //     // 关闭时清空美术配置
    //     this.editForm.artConfig = {
    //       ignorePath: [],
    //       ruleConfigFile: null
    //     };
    //     this.editRuleConfigFileList = [];
    //     this.editArtInputVisible = false;
    //     this.editArtIgnorePath = '';
    //   }
    // },

    // 监听编辑表单的仓库类型变化
    'editForm.tableConfig.type'(newVal) {
      if (newVal !== 'git') {
        // 非Git时清空Git分支名称
        this.editForm.tableConfig.gitBranch = '';
      }
    }
  },
  methods: {
    async fetchBranchList() {
      console.log('获取分支列表，项目ID:', this.projectId);

      this.branchListLoading = true;
      try {
        const res = await getProjectBranchs({ projectId: this.projectId });
        if (res.code === 200) {
          // 直接使用API返回的数据结构
          this.branchList = res.data.map((item) => ({
            id: item.id,
            name: item.name || item.branch,
            branch: item.branch,
            art: item.art,
            table: item.table,
            error_rule_num: item.error_rule_num,
            scan_time: item.scan_time,
            created_time: item.created_time,
            updated_time: item.updated_time,
            project_id: item.project_id,
            ignorePath: item.ignorePath,
            tableSuffix: item.tableSuffix,
            tableConfig: item.tableConfig,
            art_filename: item.art_filename,
            repository_info: item.repository_info
          }));
        } else {
          this.$message.error(res.msg || '获取分支列表失败');
        }
      } catch (error) {
        console.error('获取分支列表失败:', error);
        this.$message.error('获取分支列表失败');
        // 发生错误时使用空数组
        this.branchList = [];
      } finally {
        this.branchListLoading = false;
      }
    },

    // 根据art和table字段确定分支检测类型
    getBranchType(branch) {
      if (branch.art && branch.table) {
        return '美术资源 + 表格资源';
      } else if (branch.art) {
        return '美术资源';
      } else if (branch.table) {
        return '表格资源';
      } else {
        return '未配置';
      }
    },

    // 选择分支
    selectBranch(branch) {
      this.selectedBranch = branch;
      this.showCreateForm = false; // 隐藏创建表单
      this.fillEditForm(branch); // 填充编辑表单
    },

    // 填充编辑表单数据
    fillEditForm(branch) {
      // 处理repository_info数据
      const repositoryInfo = branch.repository_info && branch.repository_info.length > 0
        ? branch.repository_info[0]
        : {};

      // 处理ignorePath数据
      const ignorePath = branch.ignorePath
        ? branch.ignorePath.split(',').filter(path => path.trim())
        : [];

      this.editForm = {
        branchName: branch.name || branch.branch,
        enableTableCheck: branch.table || false,
        enableArtCheck: branch.art || false,
        tableConfig: {
          tableSuffix: branch.tableSuffix || '',
          head_row: branch.tableConfig?.HEAD_ROW || '',
          head_row_cn: branch.tableConfig?.HEAD_ROW_CN || '',
          row_start: branch.tableConfig?.ROW_START || '',
          col_start: branch.tableConfig?.COL_START || '',
          type: repositoryInfo.stype || '',
          url: repositoryInfo.url || '',
          gitBranch: repositoryInfo.repository_branch || '',
          username: repositoryInfo.user || '',
          password: repositoryInfo.password || ''
        },
        artConfig: {
          ignorePath: ignorePath,
          ruleConfigFile: null
        }
      };

      // 重置编辑相关的UI状态
      this.editRuleConfigFileList = [];
      this.editArtInputVisible = false;
      this.editArtIgnorePath = '';
    },





    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      return date.toLocaleString('zh-CN');
    },

    // 创建分支
    handleCreateBranch() {
      this.showCreateForm = true;
      this.selectedBranch = null; // 清除选中的分支
      this.resetCreateForm();
    },

    // 重置创建表单
    resetCreateForm() {
      this.createForm = {
        branchName: '',
        enableTableCheck: false,
        enableArtCheck: false,
        tableConfig: {
          tableSuffix: '',
          head_row: '',
          head_row_cn: '',
          row_start: '',
          col_start: '',
          type: '',
          url: '',
          gitBranch: '',
          username: '',
          password: ''
        },
        artConfig: {
          ignorePath: [],
          ruleConfigFile: null
        }
      };
      this.ruleConfigFileList = [];
      this.artInputVisible = false;
      this.artIgnorePath = '';
      if (this.$refs.branchForm) {
        this.$refs.branchForm.resetFields();
      }
    },

    // 统一的表单提交方法
    async submitForm() {
      if (this.selectedBranch) {
        await this.submitEditForm();
      } else {
        await this.submitCreateForm();
      }
    },

    // 统一的取消方法
    cancelForm() {
      if (this.selectedBranch) {
        this.cancelEdit();
      } else {
        this.cancelCreate();
      }
    },

    // 取消创建
    cancelCreate() {
      // 清空所有内容，保持新建状态
      this.resetCreateForm();
    },

    // 处理rule_config文件变化
    handleRuleConfigChange(file, fileList) {
      // 验证文件类型
      if (!file.name.endsWith('.json')) {
        this.$message.error('只能上传JSON文件');
        return false;
      }

      // 验证文件大小（10MB）
      if (file.size > 10 * 1024 * 1024) {
        this.$message.error('文件大小不能超过10MB');
        return false;
      }

      this.ruleConfigFileList = fileList;
      this.currentForm.artConfig.ruleConfigFile = file.raw;
    },

    // 移除rule_config文件
    handleRuleConfigRemove(file, fileList) { // eslint-disable-line no-unused-vars
      this.ruleConfigFileList = fileList;
      this.currentForm.artConfig.ruleConfigFile = null;
    },

    // 下载rule_config文件
    async downloadRuleConfigFile() {
      if (!this.selectedBranch || !this.selectedBranch.art_filename) {
        this.$message.warning('没有可下载的文件');
        return;
      }

      try {
        // 调用真实的下载API，获取文件流
        const blob = await downloadRuleConfigFile({
          projectId: this.projectId,
          branch: this.selectedBranch.name || this.selectedBranch.branch
        });

        // 检查响应是否存在
        if (blob) {
          const filename = this.selectedBranch.art_filename;

          // 创建下载链接
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = filename;

          // 触发下载
          document.body.appendChild(link);
          link.click();

          // 清理
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);

          this.$message.success('文件下载成功');
        } else {
          this.$message.error('下载文件失败');
        }
      } catch (error) {
        console.error('下载文件失败:', error);
        this.$message.error('下载文件失败，请重试');
      }
    },

    // 美术资源忽略路径相关方法
    showArtInput() {
      this.artInputVisible = true;
      this.$nextTick(() => {
        this.$refs.artSaveTagInput.$refs.input.focus();
      });
    },

    handleArtInputConfirm() {
      const inputValue = this.artIgnorePath;
      if (inputValue) {
        for (const path of this.currentForm.artConfig.ignorePath) {
          if (path === inputValue) {
            this.$message({
              type: 'warning',
              message: '路径已存在'
            });
            this.artIgnorePath = '';
            this.$nextTick(() => {
              this.$refs.artSaveTagInput.$refs.input.focus();
            });
            return;
          }
        }
      }
      if (this.artIgnorePath !== '') {
        this.currentForm.artConfig.ignorePath.push(this.artIgnorePath);
        this.artInputVisible = false;
        this.artIgnorePath = '';
      } else {
        this.artInputVisible = false;
        this.artIgnorePath = '';
      }
    },

    removeArtIgnorePath(path) {
      const index = this.currentForm.artConfig.ignorePath.indexOf(path);
      if (index > -1) {
        this.currentForm.artConfig.ignorePath.splice(index, 1);
      }
    },

    // 编辑表单相关方法
    // 编辑美术资源忽略路径相关方法
    showEditArtInput() {
      this.editArtInputVisible = true;
      this.$nextTick(() => {
        this.$refs.editArtSaveTagInput.$refs.input.focus();
      });
    },

    handleEditArtInputConfirm() {
      const inputValue = this.editArtIgnorePath;
      if (inputValue) {
        for (const path of this.editForm.artConfig.ignorePath) {
          if (path === inputValue) {
            this.$message({
              type: 'warning',
              message: '路径已存在'
            });
            this.editArtIgnorePath = '';
            this.$nextTick(() => {
              this.$refs.editArtSaveTagInput.$refs.input.focus();
            });
            return;
          }
        }
      }
      if (this.editArtIgnorePath !== '') {
        this.editForm.artConfig.ignorePath.push(this.editArtIgnorePath);
        this.editArtInputVisible = false;
        this.editArtIgnorePath = '';
      } else {
        this.editArtInputVisible = false;
        this.editArtIgnorePath = '';
      }
    },

    removeEditArtIgnorePath(path) {
      const index = this.editForm.artConfig.ignorePath.indexOf(path);
      if (index > -1) {
        this.editForm.artConfig.ignorePath.splice(index, 1);
      }
    },

    // 处理编辑rule_config文件变化
    handleEditRuleConfigChange(file, fileList) {
      // 验证文件类型
      if (!file.name.endsWith('.json')) {
        this.$message.error('只能上传JSON文件');
        return false;
      }

      // 验证文件大小（10MB）
      if (file.size > 10 * 1024 * 1024) {
        this.$message.error('文件大小不能超过10MB');
        return false;
      }

      this.editRuleConfigFileList = fileList;
      this.editForm.artConfig.ruleConfigFile = file.raw;
    },

    // 移除编辑rule_config文件
    handleEditRuleConfigRemove(file, fileList) { // eslint-disable-line no-unused-vars
      this.editRuleConfigFileList = fileList;
      this.editForm.artConfig.ruleConfigFile = null;
    },

    // 提交编辑表单
    async submitEditForm() {
      try {
        // 验证基本表单
        const valid = await this.$refs.branchForm.validate();
        if (!valid) return;

        // 验证至少配置一种资源检查
        if (!this.editForm.enableArtCheck && !this.editForm.enableTableCheck) {
          this.$message.error('请至少配置一种资源检查（美术资源检查或表格资源检查）');
          return;
        }

        // 验证表格资源配置
        if (this.editForm.enableTableCheck) {
          if (!this.editForm.tableConfig.type) {
            this.$message.error('请选择仓库类型');
            return;
          }
          if (!this.editForm.tableConfig.url) {
            this.$message.error('请输入仓库链接');
            return;
          }
          if (this.editForm.tableConfig.type === 'git' && !this.editForm.tableConfig.gitBranch) {
            this.$message.error('请输入Git分支名称');
            return;
          }
          if (!this.editForm.tableConfig.username) {
            this.$message.error('请输入用户名');
            return;
          }
          if (!this.editForm.tableConfig.password) {
            this.$message.error('请输入密码');
            return;
          }
        }

        // 验证美术资源配置
        if (this.editForm.enableArtCheck) {
          // 编辑模式下的验证逻辑
          const hasExistingFile = this.selectedBranch.art_filename; // 是否已有文件
          const hasNewFile = this.editForm.artConfig.ruleConfigFile; // 是否上传了新文件

          // 如果既没有已存在的文件，也没有上传新文件，则提示错误
          if (!hasExistingFile && !hasNewFile) {
            this.$message.error('请上传rule_config文件');
            return;
          }
        }

        this.editLoading = true;

        // 准备提交数据
        const formData = this.prepareEditFormData();

        // TODO: 调用编辑分支API
        console.log('提交编辑FormData数据');
        const res = await editBranch(formData);
        this.$message.success('分支修改成功');
        this.fetchBranchList(); // 刷新分支列表

      } catch (error) {
        console.error('修改分支失败:', error);
        this.$message.error('修改分支失败，请重试');
      } finally {
        this.editLoading = false;
      }
    },

    // 准备编辑FormData提交数据
    prepareEditFormData() {
      const formData = new FormData();

      // 基本字段
      formData.append('id', this.selectedBranch.id);
      formData.append('projectId', this.projectId);
      formData.append('branch', this.editForm.branchName);

      // 美术资源配置
      formData.append('art', this.editForm.enableArtCheck ? 'true' : 'false');

      // 表格资源配置
      formData.append('table', this.editForm.enableTableCheck ? 'true' : 'false');

      // 如果开启了美术资源检查，添加相关字段
      if (this.editForm.enableArtCheck) {
        // 上传JSON文件（只有在上传了新文件时才添加）
        if (this.editForm.artConfig.ruleConfigFile) {
          formData.append('json', this.editForm.artConfig.ruleConfigFile);
        }
        // 如果没有新文件但有已存在的文件，可以添加文件名标识（根据后端需求）
        else if (this.selectedBranch.art_filename) {
          // 可以添加一个标识表示保持原有文件
          // formData.append('keepExistingFile', 'true');
        }

        // 忽略路径，转换为字符串
        if (this.editForm.artConfig.ignorePath && this.editForm.artConfig.ignorePath.length > 0) {
          formData.append('ignorePath', this.editForm.artConfig.ignorePath.join(','));
        }
      }

      // 如果开启了表格资源检查，添加相关字段
      if (this.editForm.enableTableCheck) {
        formData.append('user', this.editForm.tableConfig.username);
        formData.append('password', this.editForm.tableConfig.password);
        formData.append('type', this.editForm.tableConfig.type);
        formData.append('url', this.editForm.tableConfig.url);

        // Git分支名称（仅当类型为git时）
        if (this.editForm.tableConfig.type === 'git' && this.editForm.tableConfig.gitBranch) {
          formData.append('gitbranch', this.editForm.tableConfig.gitBranch);
        }

        // 表格配置参数
        formData.append('tableSuffix', this.editForm.tableConfig.tableSuffix);
        formData.append('head_row', this.editForm.tableConfig.head_row);
        formData.append('head_row_cn', this.editForm.tableConfig.head_row_cn);
        formData.append('row_start', this.editForm.tableConfig.row_start);
        formData.append('col_start', this.editForm.tableConfig.col_start);
      }

      return formData;
    },

    // 取消编辑
    cancelEdit() {
      // 切换到新建状态
      this.selectedBranch = null;
      this.showCreateForm = true;
      this.resetEditForm();
      this.resetCreateForm(); // 同时重置新建表单，确保是干净的状态
    },

    // 重置编辑表单
    resetEditForm() {
      this.editForm = {
        branchName: '',
        enableTableCheck: false,
        enableArtCheck: false,
        tableConfig: {
          tableSuffix: '',
          head_row: '',
          head_row_cn: '',
          row_start: '',
          col_start: '',
          type: '',
          url: '',
          gitBranch: '',
          username: '',
          password: ''
        },
        artConfig: {
          ignorePath: [],
          ruleConfigFile: null
        }
      };
      this.editRuleConfigFileList = [];
      this.editArtInputVisible = false;
      this.editArtIgnorePath = '';
      if (this.$refs.branchForm) {
        this.$refs.branchForm.resetFields();
      }
    },



    // 提交创建表单
    async submitCreateForm() {
      try {
        // 验证基本表单
        const valid = await this.$refs.branchForm.validate();
        if (!valid) return;

        // 验证至少配置一种资源检查
        if (!this.createForm.enableArtCheck && !this.createForm.enableTableCheck) {
          this.$message.error('请至少配置一种资源检查（美术资源检查或表格资源检查）');
          return;
        }

        // 验证表格资源配置
        if (this.createForm.enableTableCheck) {
          if (!this.createForm.tableConfig.type) {
            this.$message.error('请选择仓库类型');
            return;
          }
          if (!this.createForm.tableConfig.url) {
            this.$message.error('请输入仓库链接');
            return;
          }
          if (this.createForm.tableConfig.type === 'git' && !this.createForm.tableConfig.gitBranch) {
            this.$message.error('请输入Git分支名称');
            return;
          }
          if (!this.createForm.tableConfig.username) {
            this.$message.error('请输入用户名');
            return;
          }
          if (!this.createForm.tableConfig.password) {
            this.$message.error('请输入密码');
            return;
          }
        }

        // 验证美术资源配置
        if (this.createForm.enableArtCheck) {
          if (!this.createForm.artConfig.ruleConfigFile) {
            this.$message.error('请上传rule_config文件');
            return;
          }
        }

        this.createLoading = true;

        // 准备提交数据
        const formData = this.prepareFormData();

        // 调用创建分支API
        const res = await createBranch(formData);

        if (res.code === 200) {
          this.$message.success('分支创建成功');
          // 保持新建表单显示状态，只重置表单内容
          this.resetCreateForm();
          this.fetchBranchList(); // 刷新分支列表
        } else {
          this.$message.error(res.msg || '创建分支失败');
        }

      } catch (error) {
        console.error('创建分支失败:', error);
        this.$message.error('创建分支失败，请重试');
      } finally {
        this.createLoading = false;
      }
    },

    // 准备FormData提交数据
    prepareFormData() {
      const formData = new FormData();

      // 基本字段
      formData.append('projectId', this.projectId);
      formData.append('branch', this.createForm.branchName);

      // 美术资源配置
      formData.append('art', this.createForm.enableArtCheck ? 'true' : 'false');

      // 表格资源配置
      formData.append('table', this.createForm.enableTableCheck ? 'true' : 'false');

      // 如果开启了美术资源检查，添加相关字段
      if (this.createForm.enableArtCheck) {
        // 上传JSON文件
        if (this.createForm.artConfig.ruleConfigFile) {
          formData.append('json', this.createForm.artConfig.ruleConfigFile);
        }

        // 忽略路径，转换为字符串
        if (this.createForm.artConfig.ignorePath && this.createForm.artConfig.ignorePath.length > 0) {
          formData.append('ignorePath', this.createForm.artConfig.ignorePath.join(','));
        }
      }

      // 如果开启了表格资源检查，添加相关字段
      if (this.createForm.enableTableCheck) {
        formData.append('user', this.createForm.tableConfig.username);
        formData.append('password', this.createForm.tableConfig.password);
        formData.append('type', this.createForm.tableConfig.type);
        formData.append('url', this.createForm.tableConfig.url);

        // Git分支名称（仅当类型为git时）
        if (this.createForm.tableConfig.type === 'git' && this.createForm.tableConfig.gitBranch) {
          formData.append('gitbranch', this.createForm.tableConfig.gitBranch);
        }

        // 表格配置参数
        formData.append('tableSuffix', this.createForm.tableConfig.tableSuffix);
        formData.append('head_row', this.createForm.tableConfig.head_row);
        formData.append('head_row_cn', this.createForm.tableConfig.head_row_cn);
        formData.append('row_start', this.createForm.tableConfig.row_start);
        formData.append('col_start', this.createForm.tableConfig.col_start);
      }

      return formData;
    },

    // 删除分支
    handleDelete(branch) {
      this.$confirm(`确定要删除分支 ${branch.name} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        // TODO: 调用删除分支API
        await deleteBranch({ projectId: this.projectId, branch: branch.name });
        this.$message.success('分支删除成功');
        this.fetchBranchList(); // 刷新分支列表;
      }).catch(() => {
        this.$message.info('已取消删除');
      });
    }
  }
};
</script>

<style scoped lang="scss">
.branch-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;

  .page-header {
    padding: 20px 24px;
    background: white;
    border-bottom: 1px solid #e4e7ed;

    h2 {
      margin: 0;
      color: #303133;
      font-size: 20px;
      font-weight: 500;
    }
  }

  .main-content {
    flex: 1;
    display: flex;
    gap: 16px;
    padding: 16px;
    overflow: hidden;

    .branch-list-section {
      width: 350px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;

      .section-header {
        padding: 13px 24px;
        border-bottom: 1px solid #e4e7ed;
        display: flex;
        justify-content: space-between;
        align-items: center;

        h3 {
          margin: 0;
          color: #303133;
          font-size: 16px;
          font-weight: 500;
        }
      }

      .branch-list {
        flex: 1;
        padding: 16px;
        overflow-y: auto;

        .branch-card {
          background: #fafafa;
          border: 1px solid #e4e7ed;
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 12px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            border-color: #409eff;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
          }

          &.active {
            border-color: #409eff;
            background: #ecf5ff;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
          }

          &:last-child {
            margin-bottom: 0;
          }

          .branch-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;

            .branch-name {
              font-size: 16px;
              font-weight: 500;
              color: #303133;
              display: flex;
              align-items: center;
              gap: 8px;
              flex: 1;

              i {
                color: #909399;
              }
            }

            .branch-actions {
              .delete-icon {
                color: #c0c4cc;
                font-size: 16px;
                cursor: pointer;
                padding: 4px;
                border-radius: 4px;
                transition: all 0.3s ease;
                opacity: 0;

                &:hover {
                  color: #f56c6c;
                  background-color: #fef0f0;
                }
              }
            }
          }

          &:hover .branch-actions .delete-icon {
            opacity: 1;
          }

          .branch-card-content {
            .branch-info {
              .info-item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 6px;
                font-size: 14px;

                &:last-child {
                  margin-bottom: 0;
                }

                .label {
                  color: #909399;
                  min-width: 70px;
                }

                .value {
                  color: #606266;
                  flex: 1;
                  text-align: right;
                  word-break: break-all;
                }
              }
            }
          }
        }

        .empty-state {
          text-align: center;
          padding: 60px 20px;
          color: #909399;

          i {
            font-size: 48px;
            margin-bottom: 16px;
            display: block;
          }

          p {
            margin: 0;
            font-size: 14px;
          }
        }
      }
    }

    .branch-detail-section {
      flex: 1;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;

      .section-header {
        padding: 20px 24px;
        border-bottom: 1px solid #e4e7ed;

        h3 {
          margin: 0;
          color: #303133;
          font-size: 16px;
          font-weight: 500;
        }
      }

      .detail-content {
        flex: 1;
        padding: 24px;
        overflow-y: auto;

        .create-branch-form {
          max-width: 1200px;
          margin: 0 auto;

          .resource-layout {
            display: flex;
            gap: 24px;
            margin: 20px 0;
            align-items: flex-start; // 让子元素顶部对齐，不拉伸高度

            .resource-section {
              flex: 1;
              background: #f8f9fa;
              border-radius: 8px;
              padding: 20px;
              border: 1px solid #e4e7ed;
              height: auto; // 自适应内容高度

              &.art-section {
                border-left: 4px solid #67c23a;
              }

              &.table-section {
                border-left: 4px solid #409eff;
              }

              .resource-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 16px;
                padding-bottom: 12px;
                border-bottom: 1px solid #e4e7ed;

                .resource-title {
                  font-size: 16px;
                  font-weight: 500;
                  color: #303133;
                }
              }

              .resource-config {
                .el-form-item {
                  margin-bottom: 16px;

                  .el-form-item__label {
                    font-size: 14px;
                    color: #606266;
                  }
                }

                .el-upload__tip {
                  margin-top: 8px;
                  font-size: 12px;
                  color: #909399;
                }

                // 标签输入样式
                .input-new-tag {
                  width: 90px;
                  margin-left: 10px;
                  vertical-align: bottom;
                }

                .button-new-tag {
                  margin-left: 10px;
                  height: 32px;
                  line-height: 30px;
                  padding-top: 0;
                  padding-bottom: 0;
                }

                .el-tag {
                  margin-right: 10px;
                  margin-bottom: 5px;
                }

                // 上传文件显示样式
                .uploaded-file-display {
                  .file-item {
                    display: flex;
                    align-items: center;
                    padding: 8px 12px;
                    background: #f5f7fa;
                    border: 1px solid #e4e7ed;
                    border-radius: 4px;
                    margin-bottom: 8px;

                    i {
                      color: #409eff;
                      margin-right: 8px;
                      font-size: 16px;
                    }

                    .file-name {
                      flex: 1;
                      color: #409eff;
                      cursor: pointer;
                      text-decoration: underline;
                      margin-right: 8px;

                      &:hover {
                        color: #66b1ff;
                      }
                    }

                    .el-tag {
                      margin: 0;
                    }
                  }

                  .file-tip {
                    display: flex;
                    align-items: center;
                    padding: 4px 8px;
                    color: #909399;
                    font-size: 12px;
                    background: #fafafa;
                    border-radius: 4px;

                    i {
                      color: #e6a23c;
                      margin-right: 4px;
                      font-size: 14px;
                    }
                  }

                  .no-file {
                    display: flex;
                    align-items: center;
                    padding: 8px 12px;
                    color: #909399;
                    font-size: 14px;

                    i {
                      color: #f56c6c;
                      margin-right: 8px;
                      font-size: 16px;
                    }
                  }
                }
              }
            }
          }

          .el-form-item {
            margin-bottom: 20px;
          }

          .el-upload__tip {
            margin-top: 8px;
            font-size: 12px;
            color: #909399;
          }
        }

        .branch-detail {
          .detail-info {
            .detail-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 24px;
              padding-bottom: 16px;
              border-bottom: 1px solid #e4e7ed;

              h4 {
                margin: 0;
                font-size: 18px;
                color: #303133;
                font-weight: 500;
              }
            }

            .detail-content-info {
              .info-card {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 20px;
                border: 1px solid #e4e7ed;

                h5 {
                  margin: 0 0 16px 0;
                  font-size: 16px;
                  color: #303133;
                  font-weight: 500;
                  border-bottom: 1px solid #e4e7ed;
                  padding-bottom: 8px;
                }

                .info-list {
                  .info-row {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 12px;

                    &:last-child {
                      margin-bottom: 0;
                    }

                    .label {
                      color: #909399;
                      font-size: 14px;
                      min-width: 80px;
                    }

                    .value {
                      color: #606266;
                      font-size: 14px;
                      text-align: right;
                      word-break: break-all;

                      &.warning-count {
                        color: #f56c6c;
                        font-weight: 500;
                      }
                    }
                  }
                }
              }

              .action-section {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 20px;
                border: 1px solid #e4e7ed;

                h5 {
                  margin: 0 0 16px 0;
                  font-size: 16px;
                  color: #303133;
                  font-weight: 500;
                  border-bottom: 1px solid #e4e7ed;
                  padding-bottom: 8px;
                }

                .el-button {
                  margin-right: 12px;
                  margin-bottom: 8px;
                }
              }
            }
          }
        }

        .no-selection {
          text-align: center;
          padding: 60px 20px;
          color: #909399;

          i {
            font-size: 48px;
            margin-bottom: 16px;
            display: block;
          }

          p {
            margin: 0;
            font-size: 14px;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .branch-container .main-content {
    .branch-list-section {
      width: 350px;
    }
  }
}

@media (max-width: 768px) {
  .branch-container .main-content {
    flex-direction: column;

    .branch-list-section {
      width: 100%;
      height: 50%;
    }

    .branch-detail-section {
      height: 50%;

      .detail-content .create-branch-form {
        max-width: 100%;

        .resource-layout {
          flex-direction: column;
          gap: 16px;
        }
      }
    }
  }
}
</style>